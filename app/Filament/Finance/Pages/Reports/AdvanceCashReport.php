<?php

namespace App\Filament\Finance\Pages\Reports;

use App\Contracts\PdfService;
use App\Exports\Finance\AdvanceCashExport;
use App\Models\Bill;
use App\Models\Currency;
use App\Models\Finance\UserCash;
use App\Models\User;
use Carbon\Carbon;
use Filament\Actions;
use Filament\Forms\Components\Select;
use Filament\Forms\Form;
use Filament\Pages\Concerns\InteractsWithFormActions;
use Filament\Pages\Page;
use Illuminate\Support\Facades\DB;
use Malzariey\FilamentDaterangepickerFilter\Fields\DateRangePicker;

class AdvanceCashReport extends Page
{
    use InteractsWithFormActions;

    protected static ?string $navigationLabel = 'Advance Cash Report';

    protected static ?string $title = 'Advance Cash Report';

    protected static ?string $navigationGroup = 'Reports';

    protected static ?int $navigationSort = 75;

    protected static string $view = 'filament.finance.pages.reports.advance-cash-report';

    public array $filters = [];

    public ?array $data = null;

    public ?array $period = null;

    public function mount(): void
    {
        $this->form->fill();
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                DateRangePicker::make('date_range')
                    ->label('Period')
                    ->required()
                    ->maxDate(today())
                    ->startDate(today()->startOfYear())
                    ->endDate(today())
                    ->columnSpan(['lg' => 2]),
                Currency::getSelectForReport(),
                Select::make('user_id')
                    ->label('User (Optional)')
                    ->placeholder('All Users')
                    ->options(fn () => User::query()
                        ->whereHas('userCashes')
                        ->orderBy('name')
                        ->get()
                        ->mapWithKeys(function ($user) {
                            return [$user->id => $user->name];
                        })
                    )
                    ->searchable(),
            ])
            ->statePath('filters')
            ->columns(3);
    }

    protected function getFormActions(): array
    {
        return [
            Actions\Action::make('filter')
                ->label('Generate Report')
                ->submit('filter'),
            Actions\ActionGroup::make([
                Actions\Action::make('download_pdf')
                    ->label('PDF')
                    ->icon('heroicon-o-document-arrow-down')
                    ->action('downloadPdf')
                    ->disabled(fn () => $this->data === null),
                Actions\Action::make('download_xlsx')
                    ->label('XLSX')
                    ->icon('phosphor-microsoft-excel-logo')
                    ->action('downloadXlsx')
                    ->disabled(fn () => $this->data === null),
            ])
                ->label('Download')
                ->icon('heroicon-o-arrow-down-tray')
                ->color('success')
                ->button(),
        ];
    }

    public function filter(): void
    {
        $data = $this->form->getState();

        $dates = explode(' - ', $data['date_range'] ?? '');
        if (count($dates) == 2) {
            $start = Carbon::createFromFormat('d/m/Y', $dates[0])->startOfDay();
            $end = Carbon::createFromFormat('d/m/Y', $dates[1])->endOfDay();

            $this->period = [
                'start' => $start->format('Y-m-d'),
                'end' => $end->format('Y-m-d'),
            ];

            // Build the query for user cash transactions
            $query = UserCash::query()
                ->with(['user', 'category'])
                ->join('users', 'users.id', '=', 'user_cashes.user_id')
                ->whereBetween('user_cashes.cashed_at', [$start, $end]);

            // Filter by specific user if selected
            if (! empty($data['user_id'])) {
                $query->where('user_cashes.user_id', $data['user_id']);
            }

            // Get all transactions ordered by user name and cashed_at date
            $transactions = $query
                ->select([
                    'user_cashes.id',
                    'user_cashes.related_type',
                    'user_cashes.related_id',
                    'users.id as user_id',
                    'users.name as user_name',
                    'user_cashes.cashed_at',
                    'user_cashes.details',
                    'user_cashes.type',
                    DB::raw('user_cashes.amount * user_cashes.exchange_rate as amount'),
                ])
                ->orderBy('users.name')
                ->orderBy('user_cashes.cashed_at')
                ->orderBy('user_cashes.id')
                ->get();

            // Group transactions by user
            $usersData = [];
            foreach ($transactions as $transaction) {
                $userId = $transaction->user_id;

                if (! isset($usersData[$userId])) {
                    $usersData[$userId] = [
                        'user' => [
                            'id' => $transaction->user_id,
                            'name' => $transaction->user_name,
                        ],
                        'transactions' => [],
                        'opening_balance' => 0,
                        'closing_balance' => 0,
                    ];
                }

                $refNumber = '';
                if ($transaction->related && $transaction->related instanceof Bill) {
                    $refNumber = $transaction->related->bill_number;
                    // } else {
                    //     $refNumber = '#' . str_pad($transaction->id, 6, '0', STR_PAD_LEFT);
                }

                $usersData[$userId]['transactions'][] = [
                    'transaction_id' => $transaction->id,
                    'date' => $transaction->cashed_at,
                    'details' => $transaction->details,
                    'ref_number' => $refNumber,
                    'type' => $transaction->type,
                    'debit' => $transaction->type === 'd' ? $transaction->amount : 0,
                    'credit' => $transaction->type === 'c' ? $transaction->amount : 0,
                    'amount' => $transaction->amount,
                ];
            }

            // Calculate opening balances and running balances
            $this->calculateBalances($usersData, $start);

            $this->data = [
                'currency_code' => $data['currency_code'],
                'exchange_rate' => Currency::getExchangeRate($data['currency_code']),
                'users' => array_values($usersData),
                'selected_user_id' => $data['user_id'] ?? null,
            ];
        } else {
            $this->data = null;
            $this->period = null;
        }
    }

    private function calculateBalances(array &$usersData, Carbon $start): void
    {
        foreach ($usersData as $userId => &$userData) {
            // Calculate opening balance (transactions before the start date)
            $openingBalance = UserCash::query()
                ->where('user_id', $userId)
                ->where('cashed_at', '<', $start)
                ->select([
                    DB::raw('SUM(CASE WHEN type = "d" THEN amount * exchange_rate ELSE 0 END) as total_debit'),
                    DB::raw('SUM(CASE WHEN type = "c" THEN amount * exchange_rate ELSE 0 END) as total_credit'),
                ])
                ->first();

            // For advance cash: debit increases balance (cash given to user), credit decreases balance (cash returned/spent)
            $userData['opening_balance'] = ($openingBalance->total_debit ?? 0) - ($openingBalance->total_credit ?? 0);

            // Calculate running balances for each transaction
            $runningBalance = $userData['opening_balance'];
            foreach ($userData['transactions'] as &$transaction) {
                // Debit increases user's advance cash balance, credit decreases it
                $runningBalance += $transaction['debit'] - $transaction['credit'];
                $transaction['balance'] = $runningBalance;
            }

            $userData['closing_balance'] = $runningBalance;
        }
    }

    public function downloadPdf(): void
    {
        if ($this->data === null) {
            return;
        }

        $pdfService = app(PdfService::class);

        $fileName = 'advance-cash-report-' . $this->period['start'] . '-to-' . $this->period['end'] . '.pdf';

        // Create the reports directory if it doesn't exist
        $reportsPath = storage_path('app/public/reports');
        if (! file_exists($reportsPath)) {
            mkdir($reportsPath, 0755, true);
        }

        $pdfPath = $pdfService
            ->view('pdf.advance-cash-report', [
                'data' => $this->data,
                'period' => $this->period,
            ])
            ->name($fileName)
            ->save($reportsPath);

        $this->dispatch('download-file', url('storage/reports/' . basename($pdfPath)));
    }

    public function downloadXlsx()
    {
        if ($this->data === null) {
            return;
        }

        $fileName = 'advance-cash-report-' . $this->period['start'] . '-to-' . $this->period['end'] . '.xlsx';

        return (new AdvanceCashExport($this->data, $this->period))->download($fileName);
    }
}
