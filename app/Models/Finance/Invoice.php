<?php

namespace App\Models\Finance;

use App\Enums\InvoiceStatus;
use App\Mail\SalesInvoice;
use App\Models\Contracts\JournalTransaction;
use App\Models\Customer;
use App\Models\Group;
use App\Models\LA\Package;
use App\Models\Traits\DefaultLogOptions;
use Filament\Actions;
use Filament\Forms;
use Filament\Tables;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use Spatie\Activitylog\Traits\LogsActivity;

class Invoice extends Model implements JournalTransaction
{
    use DefaultLogOptions;
    use HasFactory;
    use LogsActivity;

    const DEFAULT_TERMS = '1. Reservation policy
- Check in after 17:00 (Local time), Check out before 14:00 (Local time).
- Urgent reservation/hotel booking on, or less than 3 days before check-in date trodden as a guarantee, booking and full payment should be done on the invoice date.

2. Amendment and Cancellation policy:
- Amendment to reduce room maximum 15 days before check-in date.
- Amendment to change room type maximum 7 days before check-in date.
- The rooming list should be submitted 7 days before check-in date.
- Cancellation 12 days before check-in will be charged as per hotel term condition.

3. Payment policy:
- 30 % payment upon invoice due date as definite booking.
- Deposit received is non-refundable.
- Full payment should be done before check-in date.';

    protected $fillable = [
        'invoice_date',
        'invoice_number',

        'customer_id',
        'group_id',
        'package_id',

        'due_date',

        'currency_code',
        'exchange_rate',

        'subject',
        'notes',
        'terms',

        'total',
        'paid',

        'status',
        'cancellation_note',

        'is_published',
    ];

    protected $casts = [
        'invoice_date' => 'datetime:Y-m-d',
        'due_date' => 'datetime:Y-m-d',
        'exchange_rate' => 'float',
        'total' => 'float',
        'paid' => 'float',
        'status' => InvoiceStatus::class,
        'is_published' => 'bool',
    ];

    public function getTransactionType(): string
    {
        return 'Invoice';
    }

    public function getTransactionNumber(): string
    {
        return $this->invoice_number;
    }

    public function getTransactionDetailAttribute(): ?string
    {
        return $this->subject;
    }

    protected static function booted()
    {
        static::creating(function (self $model) {
            $model->invoice_number ??= static::getNextInvoiceNumber();
        });
        static::saving(function (self $model) {
            $model->total = $model->getTotalInvoice();
            $model->paid = $model->getTotalPayment();
            $model->status = $model->getStatus();
        });
        static::saved(function (self $model) {
            $model->syncJournalEntry();
        });
    }

    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    public function group(): BelongsTo
    {
        return $this->belongsTo(Group::class);
    }

    public function package(): BelongsTo
    {
        return $this->belongsTo(Package::class);
    }

    public function items(): HasMany
    {
        return $this->hasMany(InvoiceItem::class, 'invoice_id')->orderBy('order_column');
    }

    public function payments(): HasMany
    {
        return $this->hasMany(InvoicePayment::class);
    }

    public function refunds(): HasMany
    {
        return $this->hasMany(InvoiceRefund::class);
    }

    public function journal_entry(): MorphOne
    {
        return $this->morphOne(JournalEntry::class, 'transaction');
    }

    public function scopeNotFullyPaid(Builder $query)
    {
        return $query
            ->whereNotIn('status', [InvoiceStatus::Cancelled, InvoiceStatus::Paid]);
    }

    public function scopeNotCancelled(Builder $query)
    {
        return $query->where('status', '!=', InvoiceStatus::Cancelled);
    }

    public function getTotalInvoice(): float
    {
        return $this->items()
            ->sum(DB::raw('quantity * unit_price'));
    }

    public function getTotalPayment(): float
    {
        return $this->payments->sum(function ($payment) {
            if ($payment->currency_code == $this->currency_code) {
                return $payment->amount;
            }

            $baseCurrency = config('finance.base_currency');

            return $payment->currency_code == $baseCurrency
                ? $payment->amount / $this->exchange_rate
                : $payment->amount * $payment->exchange_rate / $this->exchange_rate;
        });
    }

    public function getStatus(): InvoiceStatus
    {
        if ($this->status == InvoiceStatus::Cancelled) {
            return InvoiceStatus::Cancelled;
        }

        $status = match (true) {
            $this->total > 0 && $this->paid >= ($this->total - 0.01) => InvoiceStatus::Paid,
            $this->paid > 0 => InvoiceStatus::PaidPartial,
            default => InvoiceStatus::Unpaid
        };

        if ($status !== InvoiceStatus::Paid && $this->due_date->isPast()) {
            $status = InvoiceStatus::Overdue;
        }

        return $status;
    }

    public function syncJournalEntry(): void
    {
        if ($this->status == InvoiceStatus::Cancelled) {
            $this->journal_entry?->delete();

            return;
        }

        /** @var JournalEntry $entry */
        $entry = $this->journal_entry()->updateOrCreate([], [
            'entry_date' => $this->invoice_date,
            'details' => "Invoice {$this->invoice_number}",
        ]);
        $entry->items()->delete();
        $entry->items()->create([
            'type' => 'd',
            'account_id' => CashAccount::query()
                ->where('code', config('finance.coa.receivable')) // piutang usaha
                ->value('id'),
            'amount' => $this->total * $this->exchange_rate,
        ]);
        $entry->items()->create([
            'type' => 'c',
            'account_id' => CashAccount::query()
                ->where('code', config('finance.coa.sales')) // penjualan
                ->value('id'),
            'amount' => $this->total * $this->exchange_rate,
        ]);
    }

    public function delete()
    {
        $this->journal_entry?->delete();

        return parent::delete();
    }

    public static function getNextInvoiceNumber()
    {
        $period = current_period();
        $periodYear = preg_replace('/[^0-9]/', '', $period->name);

        $prefix = "INV-{$periodYear}";

        $lastInvoice = static::query()
            ->where('invoice_number', 'like', $prefix . '%')
            ->orderBy('invoice_number', 'desc')
            ->first();
        $lastNumber = $lastInvoice ?
            (int) str_replace($prefix, '', $lastInvoice->invoice_number)
            : 0;

        return $prefix . str_pad($lastNumber + 1, 4, '0', STR_PAD_LEFT);
    }

    public static function getSendTableAction()
    {
        return Tables\Actions\Action::make('send')
            ->action(function ($record, $data, $action) {
                Mail::to($data['email'])
                    ->send(
                        new SalesInvoice($record)
                    );
                $action->success();
            })
            ->successNotificationTitle('Invoice sent.')
            ->form(function ($record) {
                return [
                    Forms\Components\TextInput::make('email')
                        ->required()
                        ->email()
                        ->default($record->customer?->email),
                ];
            })
            ->modalWidth('sm')
            ->modalHeading('Send invoice')
            ->modalSubmitActionLabel('Send')
            ->color('success')
            ->icon('heroicon-o-paper-airplane');
    }

    public static function getSendPageAction()
    {
        return Actions\Action::make('send')
            ->action(function ($record, $data, $action) {
                Mail::to($data['email'])
                    ->send(
                        new SalesInvoice($record)
                    );
                $action->success();
            })
            ->successNotificationTitle('Invoice sent.')
            ->form(function ($record) {
                return [
                    Forms\Components\TextInput::make('email')
                        ->required()
                        ->email()
                        ->default($record->customer?->email),
                ];
            })
            ->modalWidth('sm')
            ->modalHeading('Send invoice')
            ->modalSubmitActionLabel('Send')
            ->color('success')
            ->icon('heroicon-o-paper-airplane');
    }

    public static function getDateTableFilter($default = null)
    {
        return Tables\Filters\Filter::make('invoice_date')
            ->form([
                Forms\Components\DatePicker::make('date_from')
                    ->default($default)
                    ->placeholder(fn ($state): string => 'Dec 18, ' . now()->subYear()->format('Y')),
                Forms\Components\DatePicker::make('date_until')
                    ->placeholder(fn ($state): string => now()->format('M d, Y')),
            ])
            ->query(function (Builder $query, array $data): Builder {
                return $query
                    ->when(
                        $data['date_from'],
                        fn (Builder $query, $date): Builder => $query->whereDate('invoice_date', '>=', $date),
                    )
                    ->when(
                        $data['date_until'],
                        fn (Builder $query, $date): Builder => $query->whereDate('invoice_date', '<=', $date),
                    );
            })
            ->indicateUsing(function (array $data): array {
                $indicators = [];
                if ($data['date_from'] ?? null) {
                    $indicators['date_from'] = 'Invoices from ' . Carbon::parse($data['date_from'])->format('d-M-y');
                }
                if ($data['date_until'] ?? null) {
                    $indicators['date_until'] = 'Invoices until ' . Carbon::parse($data['date_until'])->format('d-M-y');
                }

                return $indicators;
            });
    }
}
