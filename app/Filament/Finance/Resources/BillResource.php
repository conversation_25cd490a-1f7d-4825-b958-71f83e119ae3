<?php

namespace App\Filament\Finance\Resources;

use App\Actions\Bill\BulkGeneratePDF;
use App\Enums\InvoiceStatus;
use App\Enums\VendorType;
use App\Filament\Actions\ActivityLogTimelineTableAction;
use App\Filament\Finance\Resources\BillResource\Pages;
use App\Filament\Finance\Resources\BillResource\Widgets;
use App\Models\Bill;
use App\Models\Currency;
use App\Models\Customer;
use App\Models\Finance\Product;
use App\Models\Finance\ProductCategory;
use App\Models\Group;
use App\Models\Vendor;
use Carbon\Carbon;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Pages\SubNavigationPosition;
use Filament\Resources\Pages\Page;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;
use Ysfkaya\FilamentPhoneInput\Forms\PhoneInput;

class BillResource extends Resource
{
    protected static ?string $model = Bill::class;

    protected static ?string $recordTitleAttribute = 'bill_number';

    protected static ?string $navigationGroup = 'Purchases';

    protected static ?int $navigationSort = 10;

    protected static SubNavigationPosition $subNavigationPosition = SubNavigationPosition::Top;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Group::make()
                    ->schema([
                        Forms\Components\Section::make()
                            ->columns()
                            ->schema(static::getFormSchema()),
                    ])
                    ->columnSpan(['lg' => 2]),

                Forms\Components\Group::make()
                    ->schema([
                        Forms\Components\Section::make()
                            ->schema([
                                Forms\Components\Placeholder::make('created_at')
                                    ->label('Created at')
                                    ->content(fn ($record) => $record->created_at?->diffForHumans()),

                                Forms\Components\Placeholder::make('updated_at')
                                    ->label('Last modified at')
                                    ->content(fn ($record) => $record->updated_at?->diffForHumans()),
                            ])
                            ->hidden(fn ($record) => $record === null),

                        Forms\Components\Section::make()
                            ->schema([
                                // Forms\Components\Select::make('order_id')
                                //     ->label('Purchase order')
                                //     ->relationship('order', 'order_number')
                                //     ->searchable()
                                //     ->preload(),

                                Forms\Components\TextInput::make('bill_number')
                                    ->default(Bill::getNextNumber())
                                    ->unique(ignoreRecord: true)
                                    ->required(),

                                Forms\Components\DatePicker::make('bill_date')
                                    ->default(now()),

                                Forms\Components\DatePicker::make('due_date')
                                    ->default(now()),

                                Forms\Components\FileUpload::make('attachment')
                                    ->imageResizeTargetWidth('720')
                                    ->imageResizeTargetHeight('720')
                                    ->imageResizeMode('contain')
                                    ->disk('s3')
                                    ->directory('purchase/bills')
                                    ->visibility('public'),
                            ]),

                        Forms\Components\Section::make()
                            ->schema([
                                Forms\Components\Select::make('currency_code')
                                    ->required()
                                    ->label('Currency')
                                    ->options(Currency::getOptions())
                                    ->default('SAR')
                                    ->live()
                                    ->afterStateUpdated(function ($state, $set) {
                                        $set('exchange_rate', 1 / Currency::getExchangeRate($state));
                                    }),

                                Forms\Components\TextInput::make('exchange_rate')
                                    ->required()
                                    ->numeric()
                                    ->live()
                                    ->helperText(fn ($state, $get) => $state > 0 && $state < 1 ? 'SAR 1 = ' . $get('currency_code') . ' ' . round(1 / $state, 2) : null)
                                    ->default(1)
                                    ->visible(fn ($get) => $get('currency_code') !== config('finance.base_currency')),
                            ]),
                    ]),

                Forms\Components\Section::make('Items')
                    ->schema(static::getFormSchema('items')),
            ])
            ->columns(3);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->recordClasses(fn ($record) => match ($record->status) {
                InvoiceStatus::Paid => 'bg-success-500/10',
                InvoiceStatus::Overdue => 'bg-danger-500/10',
                InvoiceStatus::Cancelled => 'bg-gray-500/10',
                InvoiceStatus::Unpaid => 'bg-warning-500/10',
                default => null
            })
            ->modifyQueryUsing(fn ($query) => $query
                ->with(['vendor', 'order', 'groups.customer'])
                ->leftJoinSub(
                    DB::table('bill_items')
                        ->select([
                            'bill_id',
                            DB::raw('COUNT(*) as items_count'),
                        ])
                        ->groupBy('bill_id'),
                    'items',
                    'bills.id',
                    '=',
                    'items.bill_id',
                ))
            ->columns([
                Tables\Columns\TextColumn::make('bill_number')
                    ->label('Number')
                    // ->description(fn ($record) => $record->order?->order_number, 'above')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('vendor.company_name')
                    ->searchable()
                    ->sortable()
                    ->description(fn ($record) => $record->vendor?->vendor_type->getLabel())
                    ->toggleable(),
                Tables\Columns\TextColumn::make('groups.name')
                    ->label('Groups')
                    ->toggleable()
                    ->listWithLineBreaks()
                    ->getStateUsing(fn ($record) => $record->groups->map(
                        fn ($group) => $group->customer->name . ' (' . $group->name . ')'
                    ))
                    ->badge(),
                Tables\Columns\TextColumn::make('bill_date')
                    ->date()
                    ->sortable()
                    ->toggleable(),
                Tables\Columns\TextColumn::make('due_date')
                    ->date()
                    ->sortable()
                    ->toggleable(),
                Tables\Columns\TextColumn::make('items_count')
                    ->label('Items')
                    ->badge(),
                Tables\Columns\TextColumn::make('total')
                    ->label('Total bill')
                    ->sortable()
                    ->currencyAuto(),
                Tables\Columns\IconColumn::make('attachment')
                    ->attachment(),
                Tables\Columns\TextColumn::make('paid')
                    ->label('Total paid')
                    ->sortable()
                    ->currencyAuto()
                    ->toggleable(),
                Tables\Columns\TextColumn::make('balance')
                    ->getStateUsing(fn ($record) => $record->total - $record->paid)
                    ->sortable()
                    ->currencyAuto()
                    ->toggleable(),
                Tables\Columns\TextColumn::make('status')
                    ->badge(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('vendor_id')
                    ->label('Vendor')
                    ->options(Vendor::query()->orderBy('company_name')->pluck('company_name', 'id'))
                    ->searchable(),
                Tables\Filters\SelectFilter::make('customer_id')
                    ->label('Customer')
                    ->options(Customer::query()->orderBy('name')->pluck('name', 'id'))
                    ->searchable()
                    ->query(fn ($query, $data) => $query->when($data['value'], fn ($query) => $query->whereHas('items', fn ($query) => $query->whereHas('group', fn ($query) => $query->where('customer_id', $data['value'])
                    )
                    )
                    )
                    ),
                Tables\Filters\Filter::make('bill_date')
                    ->form([
                        Forms\Components\DatePicker::make('date_from')
                            ->default(today()->startOfYear())
                            ->placeholder(fn ($state): string => 'Dec 18, ' . now()->subYear()->format('Y')),
                        Forms\Components\DatePicker::make('date_until')
                            ->placeholder(fn ($state): string => now()->format('M d, Y')),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['date_from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('bill_date', '>=', $date),
                            )
                            ->when(
                                $data['date_until'],
                                fn (Builder $query, $date): Builder => $query->whereDate('bill_date', '<=', $date),
                            );
                    })
                    ->indicateUsing(function (array $data): array {
                        $indicators = [];
                        if ($data['date_from'] ?? null) {
                            $indicators['date_from'] = 'Bills from ' . Carbon::parse($data['date_from'])->format('d-M-y');
                        }
                        if ($data['date_until'] ?? null) {
                            $indicators['date_until'] = 'Bills until ' . Carbon::parse($data['date_until'])->format('d-M-y');
                        }

                        return $indicators;
                    }),
            ])
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\ViewAction::make(),
                    Tables\Actions\EditAction::make(),
                    Tables\Actions\DeleteAction::make(),
                    Tables\Actions\ActionGroup::make([
                        Tables\Actions\Action::make('cancel')
                            ->label('Cancel')
                            ->icon('heroicon-m-x-mark')
                            ->color('danger')
                            ->action(function ($record, $data) {
                                $record->update([
                                    ...$data,
                                    'status' => InvoiceStatus::Cancelled,
                                ]);
                            })
                            ->modalHeading('Cancel Bill')
                            ->form([
                                Forms\Components\Textarea::make('cancellation_note')
                                    ->required(),
                            ])
                            ->requiresConfirmation()
                            ->visible(fn ($record) => $record->status !== InvoiceStatus::Cancelled),
                    ])
                        ->dropdown(false),
                    Tables\Actions\ActionGroup::make([
                        Tables\Actions\Action::make('payments')
                            ->icon('heroicon-m-banknotes')
                            ->url(fn ($record) => static::getUrl('payments', ['record' => $record])),
                        // Tables\Actions\Action::make('refunds')
                        //     ->icon('heroicon-m-receipt-refund')
                        //     ->url(fn ($record) => static::getUrl('refunds', ['record' => $record]))
                        //     ->visible(fn ($record) => $record->status === InvoiceStatus::Cancelled),
                    ])
                        ->dropdown(false),
                    Tables\Actions\ActionGroup::make([
                        ActivityLogTimelineTableAction::make('history'),
                    ])
                        ->dropdown(false),
                ]),
            ])
            ->bulkActions([
                Tables\Actions\BulkAction::make('download')
                    ->icon('heroicon-o-arrow-down-tray')
                    ->color('gray')
                    ->action(function ($records) {
                        return response()->download(BulkGeneratePDF::run($records));
                    })
                    ->deselectRecordsAfterCompletion(),
            ])
            ->defaultSort('bill_date', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListBills::route('/'),
            'create' => Pages\CreateBill::route('/create'),
            'view' => Pages\ViewBill::route('/{record}'),
            'edit' => Pages\EditBill::route('/{record}/edit'),
            'payments' => Pages\Payments::route('/{record}/payments'),
        ];
    }

    public static function getRecordSubNavigation(Page $page): array
    {
        return $page->generateNavigationItems([
            Pages\ViewBill::class,
            Pages\EditBill::class,
            Pages\Payments::class,
        ]);
    }

    public static function getNavigationBadge(): ?string
    {
        $count = static::$model::where('status', '!=', 'paid')->count();

        return $count > 0 ? $count : null;
    }

    public static function getFormSchema(?string $section = null): array
    {
        if ($section === 'items') {
            return [
                Forms\Components\Repeater::make('items')
                    ->relationship()
                    ->orderColumn('order_column')
                    ->reorderableWithButtons()
                    ->schema([
                        Group::formFieldSelectGroup()
                            ->columnSpanFull(),

                        Forms\Components\Group::make()
                            ->schema([
                                Forms\Components\Select::make('product_id')
                                    ->label('Item')
                                    ->relationship('product', 'name')
                                    ->searchable()
                                    ->preload()
                                    ->required()
                                    ->live()
                                    ->afterStateUpdated(function ($state, $set, $get) {
                                        $item = Product::query()->find($state);

                                        if ($item) {
                                            $set('name', $item->name ?? '');
                                            $set('description', $item->description ?? '');
                                            $set('unit_price', ($item->cost ?? $item->unit_price ?? 0) / ($get('../../exchange_rate') ?? 1));
                                            $set('vat', $item->vat ?? 0);
                                        }
                                    })
                                    ->createOptionForm([
                                        Forms\Components\Select::make('category_id')
                                            ->label('Category')
                                            ->options(ProductCategory::query()->pluck('name', 'id'))
                                            ->searchable(),
                                        Forms\Components\TextInput::make('name')
                                            ->required(),
                                        Forms\Components\Textarea::make('description'),
                                        Forms\Components\TextInput::make('cost')
                                            ->numeric()
                                            ->prefix('SAR')
                                            ->required(),
                                    ])
                                    ->createOptionAction(fn ($action) => $action->modalWidth('sm')),
                                Forms\Components\Hidden::make('name'),
                                Forms\Components\Textarea::make('description')
                                    ->rows(2)
                                    ->hiddenLabel()
                                    ->extraAttributes([
                                        'class' => 'text-sm',
                                    ]),
                            ])
                            ->columnSpan([
                                'md' => 5,
                            ]),

                        Forms\Components\TextInput::make('quantity')
                            ->numeric()
                            ->default(1)
                            ->columnSpan([
                                'md' => 2,
                            ])
                            ->required(),

                        Forms\Components\TextInput::make('unit_price')
                            ->label('Unit Price')
                            ->numeric()
                            ->required()
                            ->prefix(fn ($get) => $get('../../currency_code') ?? 'SAR')
                            ->columnSpan([
                                'md' => 3,
                            ]),

                        Forms\Components\TextInput::make('vat')
                            ->label('VAT')
                            ->numeric()
                            ->suffix('%', true)
                            ->default(0)
                            ->required()
                            ->hintIcon('heroicon-m-question-mark-circle', 'Value-Added Tax')
                            ->columnSpan([
                                'md' => 2,
                            ]),
                    ])
                    ->defaultItems(1)
                    ->hiddenLabel()
                    ->columns([
                        'md' => 12,
                    ])
                    ->required(),
            ];
        }

        return [
            Forms\Components\Select::make('vendor_type')
                ->options(VendorType::class)
                ->required()
                ->live()
                ->afterStateUpdated(fn ($set) => $set('vendor_id', null)),

            Forms\Components\Select::make('vendor_id')
                ->relationship('vendor', 'company_name', fn ($query, $get) => $query->where('vendor_type', $get('vendor_type')))
                ->searchable()
                ->preload()
                ->required()
                ->visible(fn ($get) => $get('vendor_type'))
                ->createOptionForm(function ($get) {
                    return [
                        Forms\Components\Hidden::make('vendor_type')
                            ->default($get('vendor_type')),

                        Forms\Components\TextInput::make('company_name')
                            ->required(),
                        Forms\Components\TextInput::make('contact_name'),
                        Forms\Components\TextInput::make('contact_email')
                            ->email(),
                        PhoneInput::make('contact_phone'),
                    ];
                })
                ->createOptionAction(function (Forms\Components\Actions\Action $action) {
                    return $action
                        ->modalHeading('Create vendor')
                        ->modalSubmitActionLabel('Create vendor')
                        ->modalWidth('lg');
                }),

            Forms\Components\TextInput::make('subject')
                ->columnSpanFull(),

            Forms\Components\Textarea::make('notes')
                ->rows(5)
                ->columnSpanFull(),
        ];
    }

    public static function getWidgets(): array
    {
        return [
            Widgets\BillsOverview::class,
        ];
    }
}
