<?php

namespace App\Filament\Finance\Resources\InvoiceResource\Pages;

use App\Actions\Invoice\BulkGeneratePaymentPDF;
use App\Enums\Finance\AccountCategory;
use App\Enums\InvoiceStatus;
use App\Enums\PaymentMethod;
use App\Filament\Finance\Resources\InvoiceResource;
use App\Models\Currency;
use App\Models\Finance\CashAccount;
use App\Models\Finance\InvoiceRefund;
use Carbon\Carbon;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Pages\ManageRelatedRecords;
use Filament\Tables;
use Filament\Tables\Table;

class Refunds extends ManageRelatedRecords
{
    protected static string $resource = InvoiceResource::class;

    protected static string $relationship = 'refunds';

    protected static ?string $navigationIcon = 'heroicon-o-receipt-refund';

    protected static ?string $navigationLabel = 'Refunds';

    public static function shouldRegisterNavigation(array $parameters = []): bool
    {
        return parent::shouldRegisterNavigation($parameters) &&
            $parameters['record']->status === InvoiceStatus::Cancelled;
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\ToggleButtons::make('payment_method')
                    ->grouped()
                    ->options(PaymentMethod::class)
                    ->default(PaymentMethod::Cash->value)
                    ->live()
                    ->afterStateUpdated(function ($state, $set) {
                        if ($state === 'deposit') {
                            $set('cash_account_id', CashAccount::query()
                                ->where('code', config('finance.coa.customer_deposit'))
                                ->first()->id);
                        } elseif ($state === 'accounts_payable') {
                            $set('cash_account_id', CashAccount::query()
                                ->where('category', AccountCategory::AccountsPayable)
                                ->first()->id);
                        } else {
                            $set('cash_account_id', null);
                        }
                    }),
                Forms\Components\Select::make('cash_account_id')
                    ->label('Account')
                    ->options(function ($get) {
                        $categories = $get('payment_method') === 'accounts_payable'
                            ? [AccountCategory::AccountsPayable]
                            : [AccountCategory::Cash, AccountCategory::Bank];

                        return CashAccount::query()
                            ->when(
                                $get('payment_method') === 'deposit',
                                fn ($query) => $query->where('code', config('finance.coa.customer_deposit')),
                                fn ($query) => $query->whereIn('category', $categories)
                            )
                            ->orderBy('code')
                            ->get()
                            ->mapWithKeys(function ($account) {
                                return [$account->id => $account->fullname];
                            });
                    })
                    ->required()
                    ->searchable()
                    ->disabled(fn ($get) => $get('payment_method') === 'deposit')
                    ->dehydrated(),
                Forms\Components\DateTimePicker::make('refunded_at')
                    ->label('Date')
                    ->required(),
                Forms\Components\TextInput::make('description')
                    ->autocomplete('off')
                    ->maxLength(255),

                Forms\Components\ToggleButtons::make('currency_code')
                    ->label('Currency')
                    ->default('SAR')
                    ->options(Currency::getOptions())
                    ->inline()
                    ->live()
                    ->afterStateUpdated(function ($state, $set) {
                        $set('exchange_rate', 1 / Currency::getExchangeRate($state));
                    }),

                Forms\Components\TextInput::make('amount')
                    ->numeric()
                    ->required()
                    ->prefix(fn ($get) => $get('currency_code') ?? 'SAR'),

                Forms\Components\TextInput::make('exchange_rate')
                    ->required()
                    ->numeric()
                    ->live()
                    ->helperText(fn ($state, $get) => $state > 0 && $state < 1 ? 'SAR 1 = ' . $get('currency_code') . ' ' . round(1 / $state, 2) : null)
                    ->visible(fn ($get) => $get('currency_code') != 'SAR'),

                Forms\Components\FileUpload::make('attachment')
                    ->imageResizeTargetWidth('720')
                    ->imageResizeTargetHeight('720')
                    ->imageResizeMode('contain')
                    ->disk('s3')
                    ->directory('invoice/refunds')
                    ->visibility('public'),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->modelLabel('refund')
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label('No.'),
                Tables\Columns\TextColumn::make('refunded_at')
                    ->label('Date')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('payment_method')
                    ->label('Via')
                    ->badge(),
                Tables\Columns\TextColumn::make('cash_account.fullname')
                    ->label('Account'),
                Tables\Columns\TextColumn::make('description')
                    ->wrap(),
                Tables\Columns\IconColumn::make('attachment')
                    ->attachment(),
                Tables\Columns\TextColumn::make('amount')
                    ->currencyAuto(),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->mountUsing(fn ($form) => $form->fill([
                        'payment_method' => 'cash',
                        'refunded_at' => Carbon::now(),
                        'currency_code' => $this->getOwnerRecord()->currency_code,
                        'exchange_rate' => 1 / Currency::getExchangeRate($this->getOwnerRecord()->currency_code),
                    ])),
            ])
            ->actions([
                InvoiceRefund::getReceiptTableAction(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                // Tables\Actions\BulkAction::make('pdf')
                //     ->label('Download PDF')
                //     ->icon('heroicon-o-arrow-down-tray')
                //     ->color('gray')
                //     ->action(function ($records) {
                //         return response()->download(BulkGeneratePaymentPDF::run($records));
                //     }),
            ])
            ->defaultSort('refunded_at', 'desc');
    }
}
