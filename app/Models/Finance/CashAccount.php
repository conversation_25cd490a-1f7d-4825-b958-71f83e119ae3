<?php

namespace App\Models\Finance;

use App\Enums\Finance\AccountCategory;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class CashAccount extends Model
{
    use HasFactory;

    protected $fillable = [
        'code',
        'category',
        'name',
        'description',

        'is_fixed',
    ];

    protected $casts = [
        'category' => AccountCategory::class,
        'is_fixed' => 'bool',
    ];

    public function fullname(): Attribute
    {
        return Attribute::get(fn () => $this->code . ' - ' . $this->name);
    }

    public function journal_entry_items(): HasMany
    {
        return $this->hasMany(JournalEntryItem::class, 'account_id');
    }

    public function scopeIsCashOrBank(Builder $query)
    {
        $query->whereIn('category', [AccountCategory::Cash, AccountCategory::Bank]);
    }

    public function scopeIsNotCashOrBank(Builder $query)
    {
        $query->whereNotIn('category', [AccountCategory::Cash, AccountCategory::Bank]);
    }
}
